import React, { ReactElement, useCallback, useEffect, useState } from 'react'
import { useRecoilState, useRecoilValue, useResetRecoilState, useSetRecoilState } from 'recoil'
import { MenuItem, OutlinedInput } from '@material-ui/core'
import { CloseRounded } from '@material-ui/icons'
// import { debounce } from 'lodash'
import { t } from 'i18next'

import { getAdById, getSearchKeywordByAdId } from '@api/common/Keyword'
import { getManualBidList, getManualBidKeywordStats } from '@api/manualBidding'
import {
  isTargetSettingReadyState,
  shoppingBidKeywordStatsState,
  shoppingBidSettingState,
  TargetBidSettingDefault
} from '@store/SsRankMaintenance'
import { useAuthority, useSession, useToast, useI18n } from '@hooks/common'
import { MediaType } from '@models/common/Media'
import { SearchingTextLimitLength } from '@models/common/CommonConstants'
import { ActionType } from '@models/common/CommonConstants'
import CustomTooltip from '@components/common/CustomTooltip'
import { ReactComponent as SearchIcon } from '@components/assets/images/icon_search.svg'
import { ModalTooltip } from '@components/common/rankMaintenance/RankTooltips'
import './RankMaintenanceTarget.scss'
import { isSuccessResponse } from '@models/common/CommonResponse'
import { FormField, FormLabel, FormSubLabel, ReadOnlyField } from '@components/rankMaintenance/BaseComponents'
import { cn } from '@utils/index'
import { EllipsisText } from '@components/common'

interface Props {
  modalType: ActionType
}

const RankMaintenanceTarget: React.FC<Props> = ({ modalType }: Props): ReactElement => {
  const { advertiser } = useAuthority()
  const { openToast } = useToast()
  const { sessionInfo } = useSession()
  const { isEN } = useI18n()
  const [hasSelectedAdId, setSelectedAdId] = useState<boolean>(false)
  const [bidSettingInfo, setBidSettingInfo] = useRecoilState(shoppingBidSettingState)
  // const resetBidSettingInfo = useResetRecoilState(shoppingBidSettingState)
  const setBidKeywordStats = useSetRecoilState(shoppingBidKeywordStatsState)
  const setIsSettingReady = useSetRecoilState(isTargetSettingReadyState)
  const resetBidKeywordStats = useResetRecoilState(shoppingBidKeywordStatsState)

  const isDuplicated = useCallback(async (advertiserId: number, adId: string) => {
    const response = await getManualBidList({
      advertiserId,
      pageSize: 20,
      pageIndex: 1,
      sorting: 'ASC',
      orderBy: 'BID_YN'
    })
    if (isSuccessResponse(response)) {
      const existing = response.data.ads.find((ad) => ad.adId == adId)
      return !!existing
    }
    return false
  }, [])

  const getKeywordTable = async () => {
    const statsRes = await getManualBidKeywordStats({
      adId: bidSettingInfo.adId,
      loginMemberId: sessionInfo.memberId ?? 0,
      advertiserId: bidSettingInfo.advertiserId
    })
    if (isSuccessResponse(statsRes)) {
      setBidKeywordStats(statsRes.data)
    } else {
      openToast('일치하는 키워드별 실적 데이터가 없습니다.')
    }
  }

  const requestSearchAdId = async () => {
    if (!bidSettingInfo.adId) return
    const getParams = {
      adTitleId: bidSettingInfo.adId,
      mediaType: bidSettingInfo.mediaType,
      advertiserId: bidSettingInfo.advertiserId
    }

    if (await isDuplicated(bidSettingInfo.advertiserId, bidSettingInfo.adId)) {
      openToast(t('rankMaintenance.message.RankMaintenanceModal.duplicateAdId'))
      return
    }

    const response = await getAdById(getParams)
    if (!response) {
      openToast(t('rankMaintenance.message.RankMaintenanceTarget.emptyAd'))
    } else {
      setSelectedAdId(true)
      setIsSettingReady(true)
      await getKeywordTable()
      setBidSettingInfo((prev) => ({
        ...prev,
        ...response
      }))
    }
  }

  const searchByKeyup = (event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>, key: string) => {
    if (
      event.key !== 'Enter' ||
      (hasSelectedAdId && key === 'adId') ||
      !bidSettingInfo.adId ||
      !event.currentTarget.value.trim()
    )
      return
    event.preventDefault()
    requestSearchAdId()
  }

  const clearValue = () => {
    openToast(t('rankMaintenance.message.RankMaintenanceTarget.changeInfo'))
    setBidSettingInfo((prev) => ({
      ...TargetBidSettingDefault,
      advertiserId: prev.advertiserId
    }))
    setBidKeywordStats([])
    setSelectedAdId(false)
  }

  useEffect(() => {
    if (modalType !== ActionType.CREATE && bidSettingInfo.adId) {
      getKeywordTable()
    }
    return () => {
      resetBidKeywordStats()
    }
  }, [bidSettingInfo.adId])
  return (
    <div id="RankMaintenanceTarget" className="w-full grid grid-cols-[2fr_3fr] gap-x-6 gap-y-3">
      <FormField id="select-adveriser">
        <FormLabel>
          {isEN ? t('common.label.advertisement-en') : t('common.label.advertisement')}
        </FormLabel>
        <ReadOnlyField data-testid="advertiserName" className="!text-opacity-100 !pr-10">
          {advertiser.advertiserName}
        </ReadOnlyField>
      </FormField>
      <FormField id="select-adId">
        <FormLabel>
          <ModalTooltip id="adId" field="target" />
          <span className="-ml-2">{isEN ? t('common.label.adId-en') : t('common.label.adId')}</span>
        </FormLabel>
        <OutlinedInput
          data-testid="adId"
          id="outlined-adornment-weight"
          value={bidSettingInfo.adId}
          placeholder={isEN ? t('common.label.adId-en') : t('common.label.adId')}
          onChange={(event) => setBidSettingInfo((prev) => ({ ...prev, adId: event.target.value.trim() }))}
          onKeyUp={(e) => searchByKeyup(e, 'adId')}
          disabled={modalType !== ActionType.CREATE}
          readOnly={hasSelectedAdId}
          endAdornment={
            (modalType !== ActionType.CREATE ? undefined : hasSelectedAdId) ? (
              <CloseRounded className="clear-icon" onClick={clearValue} />
            ) : (
              <SearchIcon
                className={cn('search-icon', modalType !== ActionType.CREATE && 'pointer-events-none')}
                onClick={requestSearchAdId}
                data-testid="searchButton"
              />
            )
          }
          aria-describedby="outlined-weight-helper-text"
          labelWidth={0}
          inputProps={{
            maxLength: SearchingTextLimitLength
          }}
        />
      </FormField>

      <FormField id="select-media">
        <FormLabel>
          <ModalTooltip id="media" field="target" />
          <span className="-ml-2">{isEN ? t('common.label.media-en') : t('common.label.media')}</span>
        </FormLabel>
        <ReadOnlyField data-testid="media" className="!text-opacity-100 !pr-10">
          {t(`common.code.media.${MediaType.NAVER}`)}
        </ReadOnlyField>
      </FormField>
      <FormField id="select-ad">
        <FormLabel>
          <ModalTooltip id="adName" field="target" />
          <span className="-ml-2">{isEN ? t('common.label.ad-en') : t('common.label.ad')}</span>
        </FormLabel>
        <ReadOnlyField data-testid="ad" className="!pr-10">
          <EllipsisText className="opacity-40 inline-block">
            {bidSettingInfo.productName ? bidSettingInfo.productName : (isEN ? t('common.label.ad-en') : t('common.label.ad'))}
          </EllipsisText>
        </ReadOnlyField>
      </FormField>
      <div></div>
      <FormField id="select-campaign-adgroup">
        <FormLabel>
          <ModalTooltip id="ss-adgroup" field="target" />
          <span className="-ml-2">{isEN ? t('common.label.campaignAdgroup-en') : t('common.label.campaignAdgroup')}</span>
        </FormLabel>
        <ReadOnlyField
          data-testid="campaignAdgroup"
          title={bidSettingInfo.campaignName ? `${bidSettingInfo.campaignName}/${bidSettingInfo.adgroupName}` : ''}
        >
          <EllipsisText className="opacity-40 inline-block">
            {bidSettingInfo.campaignName
              ? `${bidSettingInfo.campaignName}/${bidSettingInfo.adgroupName}`
              : (isEN ? t('common.label.campaignAdgroup-en') : t('common.label.campaignAdgroup'))}
          </EllipsisText>
        </ReadOnlyField>
      </FormField>
    </div>
  )
}

export default RankMaintenanceTarget
