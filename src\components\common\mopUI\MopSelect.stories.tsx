// src/components/common/mopUI/MopSelect.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import MopSelect from './MopSelect';
import { action } from '@storybook/addon-actions';
import { useState } from 'react';

const meta: StorybookMeta<typeof MopSelect> = {
  title: 'Components/Common/MopUI/MopSelect',
  component: MopSelect,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    id: {
      control: 'text',
      description: 'Unique identifier for the select component',
    },
    value: {
      control: 'text',
      description: 'Current selected value(s)',
    },
    onChange: {
      action: 'onChange',
      description: 'Callback function triggered when selection changes',
    },
    options: {
      control: 'object',
      description: 'Array of options - can be strings or objects with label/value properties',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text shown when no option is selected',
    },
    disabled: {
      control: 'boolean',
      description: 'Disabled state of the select component',
    },
    multiple: {
      control: 'boolean',
      description: 'Whether multiple selections are allowed',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Wrapper component for controlled state in stories
const SelectWrapper = ({ value: initialValue, ...props }: any) => {
  const [value, setValue] = useState(initialValue);
  
  return (
    <div style={{ width: '300px' }}>
      <MopSelect
        {...props}
        value={value}
        onChange={(newValue) => {
          setValue(newValue);
          action('onChange')(newValue);
        }}
      />
    </div>
  );
};

// Default story with string options
export const Default: Story = {
  render: (args) => <SelectWrapper {...args} />,
  args: {
    id: 'default-select',
    value: '',
    options: ['옵션 1', '옵션 2', '옵션 3', '옵션 4'],
    placeholder: '옵션을 선택하세요',
    disabled: false,
    multiple: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Basic MopSelect component. Uses string array as options and allows single selection.',
      },
    },
  },
};

// With object options
export const WithObjectOptions: Story = {
  render: (args) => <SelectWrapper {...args} />,
  args: {
    id: 'object-options-select',
    value: '',
    options: [
      { label: '서울특별시', value: 'seoul' },
      { label: '부산광역시', value: 'busan' },
      { label: '대구광역시', value: 'daegu' },
      { label: '인천광역시', value: 'incheon' },
      { label: '광주광역시', value: 'gwangju' },
    ],
    placeholder: '지역을 선택하세요',
    disabled: false,
    multiple: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSelect component using object-type options. Allows separate management of label and value.',
      },
    },
  },
};

// Multiple selection
export const MultipleSelection: Story = {
  render: (args) => <SelectWrapper {...args} />,
  args: {
    id: 'multiple-select',
    value: [],
    options: [
      { label: 'React', value: 'react' },
      { label: 'Vue.js', value: 'vue' },
      { label: 'Angular', value: 'angular' },
      { label: 'Svelte', value: 'svelte' },
      { label: 'Next.js', value: 'nextjs' },
    ],
    placeholder: '기술 스택을 선택하세요',
    disabled: false,
    multiple: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSelect component with multiple selection capability. Multiple options can be selected simultaneously.',
      },
    },
  },
};

// Disabled state
export const Disabled: Story = {
  render: (args) => <SelectWrapper {...args} />,
  args: {
    id: 'disabled-select',
    value: 'option1',
    options: ['옵션 1', '옵션 2', '옵션 3'],
    placeholder: '비활성화된 선택박스',
    disabled: true,
    multiple: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Disabled MopSelect component. User interaction is not possible.',
      },
    },
  },
};

// With pre-selected value
export const WithPreselectedValue: Story = {
  render: (args) => <SelectWrapper {...args} />,
  args: {
    id: 'preselected-select',
    value: 'medium',
    options: [
      { label: '소형 (Small)', value: 'small' },
      { label: '중형 (Medium)', value: 'medium' },
      { label: '대형 (Large)', value: 'large' },
      { label: '특대형 (Extra Large)', value: 'xl' },
    ],
    placeholder: '크기를 선택하세요',
    disabled: false,
    multiple: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSelect component with pre-selected initial value.',
      },
    },
  },
};

// With many options (scrollable)
export const WithManyOptions: Story = {
  render: (args) => <SelectWrapper {...args} />,
  args: {
    id: 'many-options-select',
    value: '',
    options: Array.from({ length: 20 }, (_, i) => ({
      label: `옵션 ${i + 1} - 긴 텍스트가 포함된 옵션입니다`,
      value: `option${i + 1}`,
    })),
    placeholder: '많은 옵션 중에서 선택하세요',
    disabled: false,
    multiple: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'MopSelect component with many options. Scrollable with limited maximum height.',
      },
    },
  },
};

// Multiple with pre-selected values
export const MultipleWithPreselected: Story = {
  render: (args) => <SelectWrapper {...args} />,
  args: {
    id: 'multiple-preselected-select',
    value: ['javascript', 'typescript'],
    options: [
      { label: 'JavaScript', value: 'javascript' },
      { label: 'TypeScript', value: 'typescript' },
      { label: 'Python', value: 'python' },
      { label: 'Java', value: 'java' },
      { label: 'C++', value: 'cpp' },
      { label: 'Go', value: 'go' },
    ],
    placeholder: '프로그래밍 언어를 선택하세요',
    disabled: false,
    multiple: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Multiple selection MopSelect component with pre-selected values.',
      },
    },
  },
};
