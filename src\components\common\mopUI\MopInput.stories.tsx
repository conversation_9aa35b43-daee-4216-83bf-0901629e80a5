// src/components/common/mopUI/MopInput.stories.tsx
import type { Meta as StorybookMeta, StoryObj } from '@storybook/react';
import MopInput from './MopInput';
import { action } from '@storybook/addon-actions';

const meta: StorybookMeta<typeof MopInput> = {
  title: 'Components/Common/MopUI/MopInput',
  component: MopInput,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      control: 'text',
      description: 'Input field name attribute',
    },
    label: {
      control: 'text',
      description: 'Label text displayed on the left side of the input',
    },
    defaultValue: {
      control: 'text',
      description: 'Default value of the input field',
    },
    originValue: {
      control: 'text',
      description: 'Original value that can be used to reset or initialize the input',
    },
    hasFocus: {
      control: 'boolean',
      description: 'Controls the focus styling and border color',
    },
    placeholder: {
      control: 'text',
      description: 'Placeholder text shown when input is empty',
    },
    disabled: {
      control: 'boolean',
      description: 'Disabled state of the input field',
    },
    type: {
      control: 'select',
      options: ['text', 'password', 'email', 'number', 'tel'],
      description: 'HTML input type',
    },
    handleChange: {
      action: 'handleChange',
      description: 'Callback function triggered when input value changes',
    },
    replaceValue: {
      action: 'replaceValue',
      description: 'Optional function to transform the input value before setting it',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    name: 'default-input',
    handleChange: action('handleChange'),
    placeholder: 'Enter text here...',
  },
  parameters: {
    docs: {
      description: {
        story: 'The default MopInput component with standard styling and centered text alignment.',
      },
    },
  },
};

// With label
export const WithLabel: Story = {
  args: {
    name: 'labeled-input',
    label: 'Username',
    handleChange: action('handleChange'),
    placeholder: 'Enter your username',
  },
  parameters: {
    docs: {
      description: {
        story: 'MopInput with a label displayed on the left side. The label takes up 1/5 of the width.',
      },
    },
  },
};

// With origin value
export const WithOriginValue: Story = {
  args: {
    name: 'origin-value-input',
    label: 'Email',
    originValue: '<EMAIL>',
    handleChange: action('handleChange'),
  },
  parameters: {
    docs: {
      description: {
        story: 'MopInput initialized with an origin value that can be used for resetting or default state.',
      },
    },
  },
};

// Without focus styling
export const WithoutFocus: Story = {
  args: {
    name: 'no-focus-input',
    label: 'Address',
    hasFocus: false,
    handleChange: action('handleChange'),
    placeholder: 'Enter your address',
  },
  parameters: {
    docs: {
      description: {
        story: 'MopInput with focus styling disabled. The border remains gray even when focused.',
      },
    },
  },
};

// Disabled state
export const Disabled: Story = {
  args: {
    name: 'disabled-input',
    label: 'Phone',
    disabled: true,
    originValue: '+****************',
    handleChange: action('handleChange'),
  },
  parameters: {
    docs: {
      description: {
        story: 'MopInput in disabled state with reduced opacity and no interaction capability.',
      },
    },
  },
};

// Different input types
export const DifferentTypes: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '16px',
      width: '400px'
    }}>
      <MopInput
        name="text-input"
        label="Text"
        type="text"
        placeholder="Text input"
        handleChange={action('handleChange')}
      />
      <MopInput
        name="email-input"
        label="Email"
        type="email"
        placeholder="<EMAIL>"
        handleChange={action('handleChange')}
      />
      <MopInput
        name="password-input"
        label="Password"
        type="password"
        placeholder="Enter password"
        handleChange={action('handleChange')}
      />
      <MopInput
        name="number-input"
        label="Number"
        type="number"
        placeholder="123"
        handleChange={action('handleChange')}
      />
      <MopInput
        name="tel-input"
        label="Phone"
        type="tel"
        placeholder="(*************"
        handleChange={action('handleChange')}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Demonstration of MopInput with different HTML input types including text, email, password, number, and telephone.',
      },
    },
  },
};

// With value replacement
export const WithValueReplacement: Story = {
  args: {
    name: 'replacement-input',
    label: 'Formatted',
    handleChange: action('handleChange'),
    replaceValue: (name: string, value: string) => {
      // Example: Convert to uppercase and remove spaces
      return value.toUpperCase().replace(/\s+/g, '');
    },
    placeholder: 'Type lowercase with spaces',
  },
  parameters: {
    docs: {
      description: {
        story: 'MopInput with a replaceValue function that transforms input (converts to uppercase and removes spaces).',
      },
    },
  },
};

// Form layout example
export const FormLayout: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '20px',
      width: '500px',
      padding: '24px',
      backgroundColor: '#f8f9fa',
      borderRadius: '8px'
    }}>
      <h3 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: 'bold' }}>
        User Registration Form
      </h3>
      <MopInput
        name="firstName"
        label="First Name"
        placeholder="Enter your first name"
        handleChange={action('handleChange')}
      />
      <MopInput
        name="lastName"
        label="Last Name"
        placeholder="Enter your last name"
        handleChange={action('handleChange')}
      />
      <MopInput
        name="email"
        label="Email"
        type="email"
        placeholder="<EMAIL>"
        handleChange={action('handleChange')}
      />
      <MopInput
        name="phone"
        label="Phone"
        type="tel"
        placeholder="+****************"
        handleChange={action('handleChange')}
      />
      <MopInput
        name="company"
        label="Company"
        placeholder="Your company name"
        hasFocus={false}
        handleChange={action('handleChange')}
      />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Example of MopInput components arranged in a form layout showing practical usage in a registration form.',
      },
    },
  },
};

// Interactive states showcase
export const InteractiveStates: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      flexDirection: 'column',
      gap: '20px',
      width: '400px'
    }}>
      <div>
        <h4 style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
          Normal State
        </h4>
        <MopInput
          name="normal"
          label="Normal"
          placeholder="Click to focus"
          handleChange={action('handleChange')}
        />
      </div>
      
      <div>
        <h4 style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
          With Value
        </h4>
        <MopInput
          name="with-value"
          label="Filled"
          originValue="This field has content"
          handleChange={action('handleChange')}
        />
      </div>
      
      <div>
        <h4 style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
          No Focus Styling
        </h4>
        <MopInput
          name="no-focus"
          label="No Focus"
          hasFocus={false}
          placeholder="Focus styling disabled"
          handleChange={action('handleChange')}
        />
      </div>
      
      <div>
        <h4 style={{ marginBottom: '8px', fontSize: '14px', fontWeight: 'bold' }}>
          Disabled
        </h4>
        <MopInput
          name="disabled"
          label="Disabled"
          disabled={true}
          originValue="Cannot edit this field"
          handleChange={action('handleChange')}
        />
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Comprehensive showcase of different interactive states including normal, filled, no-focus, and disabled states.',
      },
    },
  },
}; 