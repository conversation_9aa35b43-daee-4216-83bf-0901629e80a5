import './CommerceAccountDetailModal.scss'
import { Dialog, Typography } from '@material-ui/core'
import { MopIcon } from '@components/common/icon'
import { MOPIcon, MopMedia, StatusCode, COMMERCE_PATH_CONNECTION } from '@models/common'
import { useTranslation } from 'react-i18next'
import FixedLayoutTable, { FixedLayoutColumn } from '@components/common/table/FixedLayoutTable'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useDialog, useToast } from '@hooks/common'
import { deleteCommerceMedia } from '@api/oauth/Oauth'
import { CommerceAccountInfo } from '@models/oauth/Oauth'
import * as OauthApi from '@api/oauth/Oauth'
import { MopButton } from '@components/common/buttons'

export interface Props {
  onClose: () => void
  open: boolean
  media: MopMedia
  customerId: string
  refetchOAuthList: () => Promise<void>
}

const CommerceAccountDetailModal = ({ open, onClose, media, customerId, refetchOAuthList }: Props) => {
  const { t } = useTranslation()
  const { openDialog } = useDialog()
  const { openToast, openExistingI18nToast } = useToast()
  const [accounts, setAccounts] = useState<CommerceAccountInfo[]>([])

  const getCommerceAccount = useCallback(async () => {
    const data = await OauthApi.getCommerceAccountList(customerId, media)
    if (data) {
      setAccounts(data)
    }
  }, [customerId, media])

  const handleDelete = (accountUid: string) => {
    openDialog({
      title: t('common.message.title.notice'),
      message: t('oauthLink.commerceAccountModal.deleteConfirm'),
      cancelLabel: t('common.label.button.cancel'),
      actionLabel: t('common.label.button.confirm'),
      onAction: async () => {
        try {
          const response = await deleteCommerceMedia(customerId, accountUid, media)

          if (response.statusCode === StatusCode.SUCCESS) {
            getCommerceAccount()
            openToast(t('common.message.deleteSuccess'))
          } else {
            openToast(t('common.message.systemError'))
          }
        } catch (error) {
          openToast(t('common.message.systemError'))
        }
      }
    })
  }

  const columns: FixedLayoutColumn<CommerceAccountInfo>[] = useMemo(
    () => [
      {
        title: t('oauthLink.commerceAccountModal.table.accountName'),
        field: 'clientName',
        cellStyle: { width: '40%', textAlign: 'center' },
        headerStyle: { textAlign: 'center' }
      },
      {
        title: t('oauthLink.commerceAccountModal.table.integratedDate'),
        field: 'integrationDatetime',
        cellStyle: { width: '40%', textAlign: 'center' },
        headerStyle: { textAlign: 'center' }
      },
      {
        title: t('oauthLink.commerceAccountModal.table.delete'),
        field: 'accountUid',
        cellStyle: { width: '20%', textAlign: 'center' },
        headerStyle: { textAlign: 'center' },
        sorting: false,
        render: (rowData) => (
          <div className="delete-icon">
            <MopIcon
              name={MOPIcon.DELETE}
              id={`delete-${rowData.accountUid}`}
              onClick={() => handleDelete(rowData.accountUid)}
            />
          </div>
        )
      }
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  )

  const openLinkCommerceAccount = () => {
    window.open(
      `${window.location.origin}/oauth-link?type=${COMMERCE_PATH_CONNECTION[media]}&customerId=${customerId}`,
      '_blank'
    )
  }

  const handleClose = () => {
    //If no account, need to reload oauth list to display correct commerce account status
    if (!accounts.length) {
      refetchOAuthList()
    }
    onClose()
  }

  const receiveLinkCommerceResponse = ({ data }: MessageEvent) => {
    if (data.type !== 'oauth' || !open) return
    openExistingI18nToast(`oauthLink.toast.${data.result}`)
    getCommerceAccount()
  }

  useEffect(() => {
    if (open) {
      getCommerceAccount()
    }
  }, [open, getCommerceAccount])

  useEffect(() => {
    window.addEventListener('message', receiveLinkCommerceResponse)
    return () => {
      window.removeEventListener('message', receiveLinkCommerceResponse)
    }
  }, []) //eslint-disable-line

  return (
    <Dialog open={open} onClose={handleClose} className="commerce-account-modal" id="CommerceAccountDetailModal">
      <section className="commerce-account-modal__title">
        <span className="commerce-account-modal__title-head">{t('oauthLink.commerceAccountModal.title')}</span>
      </section>
      <section className="commerce-account-modal__content">
        <div className="commerce-account-modal__table-action">
          <Typography variant="h6">{t('oauthLink.commerceAccountModal.table.title')}</Typography>
          <MopButton
            bgColor="#ffffff"
            borderColor="#5472FF"
            contained
            label={t('oauthLink.commerceAccountModal.button.addIntegration')}
            onClick={openLinkCommerceAccount}
            rightIcon="PLUS"
            rounded="full"
            size="md"
            textColor="#5472FF"
          />
        </div>
        <div className="commerce-account-modal__table-wrapper">
          <FixedLayoutTable
            id="commerce-account-modal-table"
            columns={columns}
            data={accounts}
            tableType="list-table"
            options={{
              sorting: false
            }}
            localization={{ body: { emptyDataSourceMessage: t('common.message.list.noData') } }}
          />
        </div>
        <div className="border border-gray-dark text-[#4b5563] rounded-md p-4 bg-white">
          <h3 className="text-base font-bold flex">
            <MopIcon name={MOPIcon.NOTICE_TRIANGLE} />
            {t('oauthLink.commerceAccountModal.notice.title')}
          </h3>
          <ul className="list-disc list-outside text-sm pl-2 ml-5">
            <li>{t('oauthLink.commerceAccountModal.notice.content1')}</li>
            <li>{t('oauthLink.commerceAccountModal.notice.content2')}</li>
          </ul>
        </div>
      </section>
    </Dialog>
  )
}

export default CommerceAccountDetailModal
