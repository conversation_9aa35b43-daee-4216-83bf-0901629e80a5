// src/components/common/mopUI/MopSelect.tsx
import React, { Fragment, useEffect, useState } from 'react'
import { Listbox, Transition } from '@headlessui/react'
import { cn } from '@utils/index'
import { MOPIcon } from '@models/common'
import MopIcon from '../icon/MopIcon'

interface SelectOption {
  label: string
  value: string
  icon?: string
}

interface Props {
  id?: string
  value: string | string[]
  onChange: (value: string | string[]) => void
  options: string[] | SelectOption[]
  placeholder?: string
  disabled?: boolean
  multiple?: boolean
}

const getSelectedLabel = (value: string, options: string[] | SelectOption[]) => {
  const option = [...options].find((option) => {
    return typeof option === 'object' ? option.value === value : option === value
  })
  if (typeof option === 'object') {
    return option.label
  }
  return option
}

const MopSelect = React.forwardRef<HTMLButtonElement, Props>(
  ({ id, value, onChange, options, placeholder = '선택하세요', disabled = false, multiple = false }, ref) => {
    const [selected, setSelected] = useState<string | string[]>(
      multiple ? (Array.isArray(value) ? value : [value]) : value
    )

    const displayValue = Array.isArray(selected)
      ? selected.length > 0
        ? selected.map((value) => getSelectedLabel(value, options)).join(', ')
        : placeholder
      : selected
      ? getSelectedLabel(selected, options)
      : placeholder

    const handleSelect = (option: string | string[]) => {
      if (Array.isArray(option)) {
        const filteredOptions = option.filter((item) => item !== '')
        onChange(filteredOptions)
      } else {
        onChange(option ?? '')
      }
    }

    useEffect(() => {
      setSelected(value)
    }, [value])

    return (
      <div className="w-full" data-testid={id}>
        <Listbox value={selected} onChange={handleSelect} multiple={multiple} disabled={disabled}>
          {({ open }) => (
            <div className="relative">
              <Listbox.Button
                ref={ref}
                className={cn(
                  'w-full h-[42px] flex items-center justify-between',
                  'border border-[#efefef] rounded-[4px] px-5 gap-2.5',
                  'bg-white text-left text-sm text-[#333]',
                  'focus:outline-none focus:border-[#17171780]',
                  'disabled:bg-[#f6f8f9] disabled:cursor-not-allowed',
                  'relative z-0',
                  {
                    'text-[#707070]': !selected || (Array.isArray(selected) && selected.length === 0),
                    'border-[#17171780]': open
                  }
                )}
              >
                <span className="truncate">{displayValue}</span>
                <MopIcon name={MOPIcon.ARROW_DOWN} size={16} />
              </Listbox.Button>

              <Transition
                as={Fragment}
                leave="transition ease-in duration-100"
                leaveFrom="opacity-100"
                leaveTo="opacity-0"
              >
                <Listbox.Options
                  className={cn(
                    'absolute top-full left-0 right-0 z-50 mt-1',
                    'bg-white border border-[#efefef] rounded-[4px]',
                    'shadow-lg max-h-60 overflow-auto',
                    'py-1'
                  )}
                  static={false}
                >
                  {options.map((option, index) => {
                    const optionValue = typeof option === 'object' ? option.value : option
                    const optionLabel = typeof option === 'object' ? option.label : option
                    const optionIcon = typeof option === 'object' ? option.icon : null

                    return (
                      <Listbox.Option key={`${optionValue}-${index}`} value={optionValue} as={Fragment}>
                        {({ active, selected }) => (
                          <li
                            className={cn(
                              'relative cursor-pointer select-none px-5 py-2',
                              'hover:bg-[#f9f9fb] hover:rounded transition-colors',
                              {
                                'bg-[#f9f9fb] rounded': active,
                                'bg-[#f9f9fb] rounded font-bold': selected
                              }
                            )}
                          >
                            <div className="flex items-center gap-2">
                              {optionIcon && <span className="w-4">{optionIcon}</span>}
                              <span className={cn('block truncate')}>
                                {optionLabel}
                              </span>
                            </div>
                          </li>
                        )}
                      </Listbox.Option>
                    )
                  })}
                </Listbox.Options>
              </Transition>
            </div>
          )}
        </Listbox>
      </div>
    )
  }
)

MopSelect.displayName = 'MopSelect'

export default MopSelect
